# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

HerdNet is a deep learning framework for detecting and counting African mammals in aerial imagery. The core model uses a DLA (Deep Layer Aggregation) backbone with specialized heads for localization and classification. The codebase supports training, testing, and inference workflows using PyTorch and Hydra for configuration management.

**Paper**: "From Crowd to Herd Counting: How to Precisely Detect and Count African Mammals using Aerial Imagery and Deep Learning?" (ISPRS Journal 2023)

## Environment Setup

```bash
# Create and activate conda environment
conda env create -f environment.yml
conda activate herdnet

# Install package
python setup.py install

# Login to Weights & Biases for experiment tracking
wandb login
```

**Requirements**: Python 3.8, PyTorch 1.11.0, CUDA 11.3

## Common Development Commands

### Creating Training/Test Patches
Extract patches from full-size images (useful for GPU memory constraints):
```bash
python tools/patcher.py <root> <height> <width> <overlap> <dest> [-csv <path>] [-min <int>] [-all]
```

### Training
Launch training with default config:
```bash
python tools/train.py
```

Use custom config (saved in `configs/train/`):
```bash
python tools/train.py train=<config_name>
```

Override parameters from command line:
```bash
python tools/train.py train=herdnet train.training_settings.batch_size=8 train.training_settings.lr=1e-3
```

Multi-run sweeps:
```bash
python tools/train.py -m train=herdnet train.training_settings.batch_size=2,4,8 train.training_settings.lr=1e-3,1e-4
```

### Testing
Launch testing with default config:
```bash
python tools/test.py
```

Use custom config (saved in `configs/test/`):
```bash
python tools/test.py test=<config_name>
```

### Inference
Run inference on new images with a pretrained model:
```bash
python tools/infer.py <root> <pth_file> [-ts <thumbnail_size>] [-pf <print_freq>] [-device <cuda|cpu>]
```

**Note**: The `.pth` file must contain `classes`, `mean`, and `std` keys for proper inference.

### Visualization
View ground truth and detections using FiftyOne:
```bash
python tools/view.py <root> <gt_csv> [-dets <detections_csv>]
```

## Architecture

### Core Components

1. **Models** (`animaloc/models/`):
   - `HerdNet`: Main model with DLA backbone, localization head (heatmap) and classification head
   - `faster_rcnn.py`: Torchvision-based Faster R-CNN implementation
   - `dla.py`, `ss_dla.py`: DLA backbone variants
   - Registry pattern: Models register via `@MODELS.register()` decorator

2. **Data Pipeline** (`animaloc/data/` and `animaloc/datasets/`):
   - `CSVDataset`: Point or bounding box annotations from CSV files
   - `PatchedDataset`: For patched images
   - `FolderDataset`: For folder-based organization
   - Transforms in `animaloc/data/transforms.py`:
     - `FIDT`: Feature Interaction Distance Transform for point annotations
     - `PointsToMask`: Converts point annotations to masks
     - `DownSample`: Downsamples annotations
     - `MultiTransformsWrapper`: Applies multiple transforms independently
   - Albumentations used for image augmentation

3. **Training** (`animaloc/train/`):
   - `Trainer`: Base trainer class handling training loop, validation, checkpointing
   - `LossWrapper`: Wraps model with multiple losses (each with lambda weighting)
   - `adaloss.py`: Adaptive loss weighting
   - Custom losses in `animaloc/train/losses/` (FocalLoss, SSIM)

4. **Evaluation** (`animaloc/eval/`):
   - `Evaluator`: Base evaluator for computing metrics
   - `HerdNetEvaluator`: Specialized evaluator using LMDS (Local Maximum Detection System)
   - `Stitcher`/`HerdNetStitcher`: Inference on large images via sliding window
   - Metrics: `PointsMetrics`, `BoxesMetrics`, `ImageLevelMetrics`
   - Detection extraction via `HerdNetLMDS` in `lmds.py`

5. **Registry System** (`animaloc/utils/registry.py`):
   - Central registration for models, datasets, transforms, losses, trainers, evaluators
   - Access via `MODELS`, `DATASETS`, `TRANSFORMS`, `LOSSES`, `TRAINERS`, `EVALUATORS`

### Data Format

**Point annotations** (CSV with header: `images,x,y,labels`):
```csv
images,x,y,labels
Example.JPG,517,1653,2
Example.JPG,800,1253,1
```

**Bounding box annotations** (CSV with header: `images,x_min,y_min,x_max,y_max,labels`):
```csv
images,x_min,y_min,x_max,y_max,labels
Example.JPG,530,1458,585,1750,4
Example.JPG,95,1321,152,1403,2
```

### Configuration System (Hydra)

Training and testing use YAML configs with Hydra framework:
- Training configs: `configs/train/*.yaml`
- Testing configs: `configs/test/*.yaml`
- Base config: `configs/config.yaml`

**Key config sections**:
1. Experiment settings (W&B project, seed, device)
2. Model definition (class name, kwargs, pretrained weights)
3. Losses (multiple losses with indices and lambda weights)
4. Datasets (train/validate splits, transforms, class definitions)
5. Training/testing settings (optimizer, LR scheduler, evaluator, stitcher)

**Important**: Config values can reference other values using `${train.path.to.value}` syntax.

## Key Implementation Details

### HerdNet Model
- **Backbone**: DLA (Deep Layer Aggregation) with configurable layers (typically 34)
- **Down ratio**: Output downsampling factor (1, 2, 4, 8, or 16)
- **Outputs**:
  - Index 0: Localization heatmap (sigmoid activated)
  - Index 1: Classification logits
- **Heads**: Localization head and classification head with configurable `head_conv` parameter

### Training Workflow
1. Model wrapped with `LossWrapper` to add multiple losses
2. Each loss specified with `output_idx`, `target_idx`, and `lambda_const`
3. Validation uses `Evaluator` with metrics (f1_score, mAP, precision, recall, MAE, RMSE)
4. Optional `Stitcher` for large image inference during validation
5. Auto LR scheduler (`ReduceLROnPlateau`) available
6. Checkpoints saved as 'best' or 'last' based on validation metric

### Detection Extraction
- HerdNet outputs heatmaps processed by `HerdNetLMDS` (Local Maximum Detection System)
- LMDS parameters: `kernel_size`, `adapt_ts` (adaptive threshold)
- Extracts local maxima as detections with confidence and class

### Pretrained Models
- Available for two datasets (Ennedi 2019 and Delplanque et al. 2022)
- Models require `classes`, `mean`, `std` metadata for inference
- License: CC BY-NC-SA-4.0 (academic research only)

## Version Information
- Current version: 0.2.1
- Paper version: v0.1.0 (see git tag)
- See `CHANGELOG.md` for version details
