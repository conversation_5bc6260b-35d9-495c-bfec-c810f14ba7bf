name: herdnet
channels:
  - pytorch
  - defaults
dependencies:
  - blas=1.0=mkl
  - brotlipy=0.7.0=py38h2bbff1b_1003
  - ca-certificates=2022.07.19=haa95532_0
  - certifi=2022.6.15=py38haa95532_0
  - cffi=1.15.1=py38h2bbff1b_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - cryptography=37.0.1=py38h21b164f_0
  - cudatoolkit=11.3.1=h59b6b97_2
  - freetype=2.10.4=hd328e21_0
  - idna=3.3=pyhd3eb1b0_0
  - intel-openmp=2021.4.0=haa95532_3556
  - jpeg=9e=h2bbff1b_0
  - lerc=3.0=hd77b12b_0
  - libdeflate=1.8=h2bbff1b_5
  - libpng=1.6.37=h2a8f88b_0
  - libtiff=4.4.0=h8a3f274_0
  - libuv=1.40.0=he774522_0
  - libwebp=1.2.2=h2bbff1b_0
  - lz4-c=1.9.3=h2bbff1b_1
  - mkl=2021.4.0=haa95532_640
  - mkl-service=2.4.0=py38h2bbff1b_0
  - mkl_fft=1.3.1=py38h277e83a_0
  - mkl_random=1.2.2=py38hf11a4ad_0
  - openssl=1.1.1q=h2bbff1b_0
  - pip=22.1.2=py38haa95532_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pysocks=1.7.1=py38haa95532_0
  - python=3.8.10=hdbf39b2_7
  - pytorch=1.11.0=py3.8_cuda11.3_cudnn8_0
  - pytorch-mutex=1.0=cuda
  - requests=2.28.1=py38haa95532_0
  - setuptools=63.4.1=py38haa95532_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.39.2=h2bbff1b_0
  - tk=8.6.12=h2bbff1b_0
  - torchvision=0.12.0=py38_cu113
  - typing_extensions=4.3.0=py38haa95532_0
  - vc=14.2=h21ff451_1
  - vs2015_runtime=14.27.29016=h5e58377_2
  - wheel=0.37.1=pyhd3eb1b0_0
  - win_inet_pton=1.1.0=py38haa95532_0
  - wincertstore=0.2=py38haa95532_2
  - xz=5.2.5=h8cc25b3_1
  - zlib=1.2.12=h8cc25b3_2
  - zstd=1.5.2=h19a0ad4_0
  - pip:
    - aiofiles==0.8.0
    - albumentations==1.0.3
    - antlr4-python3-runtime==4.8
    - anyio==3.6.1
    - argcomplete==2.0.0
    - backports-zoneinfo==0.2.1
    - boto3==1.24.56
    - botocore==1.27.56
    - click==8.1.3
    - colorama==0.4.5
    - configparser==5.3.0
    - cycler==0.11.0
    - deprecated==1.2.13
    - dill==*******
    - dnspython==2.2.1
    - docker-pycreds==0.4.0
    - eventlet==0.33.1
    - fiftyone==0.14.3
    - fiftyone-brain==0.7.3
    - fiftyone-db==0.3.0
    - future==0.18.2
    - gitdb==4.0.9
    - gitpython==3.1.27
    - glob2==0.7
    - greenlet==1.1.2
    - h11==0.12.0
    - httpcore==0.15.0
    - httpx==0.23.0
    - hydra-core==1.1.0
    - imageio==2.9.0
    - imgaug==0.4.0
    - importlib-resources==5.9.0
    - jinja2==3.1.2
    - jmespath==1.0.1
    - joblib==1.1.0
    - kaleido==0.2.1
    - kiwisolver==1.4.4
    - markupsafe==2.1.1
    - matplotlib==3.4.1
    - mongoengine==0.20.0
    - motor==2.5.1
    - ndjson==0.3.1
    - networkx==2.8.6
    - numpy==1.19.2
    - omegaconf==2.1.2
    - opencv-python==4.5.1.48
    - opencv-python-headless==4.5.1.48
    - packaging==21.3
    - pandas==1.2.3
    - pathtools==0.1.2
    - patool==1.12
    - pillow==8.2.0
    - plotly==4.14.3
    - pprintpp==0.4.0
    - promise==2.3
    - protobuf==3.20.0
    - psutil==5.9.1
    - pymongo==3.12.3
    - pyparsing==3.0.9
    - python-dateutil==2.8.2
    - pytz==2022.2.1
    - pytz-deprecation-shim==0.1.0.post0
    - pywavelets==1.3.0
    - pyyaml==5.4.1
    - retrying==1.3.3
    - rfc3986==1.5.0
    - s3transfer==0.6.0
    - scikit-image==0.18.1
    - scikit-learn==1.0.2
    - scipy==1.6.2
    - seaborn==0.11.2
    - sentry-sdk==1.9.0
    - shapely==1.8.4
    - shortuuid==1.0.9
    - smmap==5.0.0
    - sniffio==1.2.0
    - sortedcontainers==2.4.0
    - subprocess32==3.5.4
    - tabulate==0.8.10
    - threadpoolctl==3.1.0
    - tifffile==2022.8.12
    - tornado==6.2
    - tqdm==4.62.3
    - tzdata==2022.2
    - tzlocal==4.2
    - universal-analytics-python3==1.1.1
    - urllib3==1.25.11
    - voxel51-eta==0.6.6
    - wandb==0.10.33
    - wrapt==1.14.1
    - xmltodict==0.13.0
    - zipp==3.8.1