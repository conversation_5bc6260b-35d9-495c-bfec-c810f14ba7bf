{"cells": [{"cell_type": "markdown", "metadata": {"id": "tOMmy2YvTHSy"}, "source": ["# DEMO - Training and testing HerdNet on nadir aerial images"]}, {"cell_type": "markdown", "metadata": {"id": "_XTpIbRwT9PO"}, "source": ["## Installations"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9v5ab5BbSrVl"}, "outputs": [], "source": ["# Check GPU\n", "!nvidia-smi"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "z9RVoQx5UOkg"}, "outputs": [], "source": ["# Install the dependencies\n", "!pip install albumentations==1.0.3\n", "!pip install fiftyone==0.14.3\n", "!pip install hydra-core==1.1.0\n", "!pip install opencv-python==********\n", "!pip install pandas==1.2.3\n", "!pip install pillow==8.2.0\n", "!pip install scikit-image==0.18.1\n", "!pip install scikit-learn==1.0.2\n", "!pip install scipy==1.6.2\n", "!pip install wandb==0.10.33"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oJydpjxoUGAC"}, "outputs": [], "source": ["# Download and install the code\n", "import sys\n", "\n", "!git clone https://github.com/Alexandre-<PERSON>/HerdNet\n", "!cd '/content/HerdNet' && python setup.py install\n", "\n", "sys.path.append('/content/HerdNet')"]}, {"cell_type": "markdown", "metadata": {"id": "cuxaC9qGVk5S"}, "source": ["## Create datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0yt06VTPVtsq"}, "outputs": [], "source": ["# Download some of the data of <PERSON><PERSON><PERSON><PERSON> et al. (2021) as an example\n", "!gdown 1CcTAZZJdwrBfCPJtVH6VBU3luGKIN9st -O /content/data.zip\n", "!unzip -oq /content/data.zip -d /content"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "y1n_cQLFYBsJ"}, "outputs": [], "source": ["# Set the seed\n", "from animaloc.utils.seed import set_seed\n", "\n", "set_seed(9292)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9tGqtLNG21Jf"}, "outputs": [], "source": ["# Create validation patches using the patcher tool (for demo)\n", "from animaloc.utils.useful_funcs import mkdir\n", "\n", "mkdir('/content/data/val_patches')\n", "!python /content/HerdNet/tools/patcher.py /content/data/val 512 512 0 /content/data/val_patches -csv /content/data/val.csv -min 0.0 -all False"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Bwp4XPR8YNMR"}, "outputs": [], "source": ["# Training, validation and test datasets\n", "import albumentations as A\n", "\n", "from animaloc.datasets import CSVDataset\n", "from animaloc.data.transforms import MultiTransformsWrapper, DownSample, PointsToMask, FIDT\n", "\n", "patch_size = 512\n", "num_classes = 7\n", "down_ratio = 2\n", "\n", "train_dataset = CSVDataset(\n", "    csv_file = '/content/data/train_patches.csv',\n", "    root_dir = '/content/data/train_patches',\n", "    albu_transforms = [\n", "        <PERSON><PERSON>(p=0.5), \n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>rightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.2),\n", "        <PERSON><PERSON>(blur_limit=15, p=0.2),\n", "        A.Normalize(p=1.0)\n", "        ],\n", "    end_transforms = [MultiTransformsWrapper([\n", "        FIDT(num_classes=num_classes, down_ratio=down_ratio),\n", "        PointsToMask(radius=2, num_classes=num_classes, squeeze=True, down_ratio=int(patch_size//16))\n", "        ])]\n", "    )\n", "\n", "val_dataset = CSVDataset(\n", "    csv_file = '/content/data/val_patches/gt.csv',\n", "    root_dir = '/content/data/val_patches',\n", "    albu_transforms = [A.Normalize(p=1.0)],\n", "    end_transforms = [DownSample(down_ratio=down_ratio, anno_type='point')]\n", "    )\n", "\n", "test_dataset = CSVDataset(\n", "    csv_file = '/content/data/test.csv',\n", "    root_dir = '/content/data/test',\n", "    albu_transforms = [A.Normalize(p=1.0)],\n", "    end_transforms = [DownSample(down_ratio=down_ratio, anno_type='point')]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lR1W5NVFYhiZ"}, "outputs": [], "source": ["# Dataloaders\n", "from torch.utils.data import DataLoader\n", "\n", "train_dataloader = DataLoader(dataset = train_dataset, batch_size = 4, shuffle = True)\n", "\n", "val_dataloader = DataLoader(dataset = val_dataset, batch_size = 1, shuffle = False)\n", "\n", "test_dataloader = DataLoader(dataset = test_dataset, batch_size = 1, shuffle = False)"]}, {"cell_type": "markdown", "metadata": {"id": "emWQUMq2Vwpj"}, "source": ["## Define HerdNet for training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JIBKygFlV0V1"}, "outputs": [], "source": ["from animaloc.models import HerdNet\n", "from torch import Tensor\n", "from animaloc.models import LossWrapper\n", "from animaloc.train.losses import FocalLoss\n", "from torch.nn import CrossEntropyLoss\n", "\n", "herdnet = HerdNet(num_classes=num_classes, down_ratio=down_ratio).cuda()\n", "\n", "weight = Tensor([0.1, 1.0, 2.0, 1.0, 6.0, 12.0, 1.0]).cuda()\n", "\n", "losses = [\n", "    {'loss': <PERSON><PERSON><PERSON><PERSON><PERSON>(reduction='mean'), 'idx': 0, 'idy': 0, 'lambda': 1.0, 'name': 'focal_loss'},\n", "    {'loss': CrossEntropyLoss(reduction='mean', weight=weight), 'idx': 1, 'idy': 1, 'lambda': 1.0, 'name': 'ce_loss'}\n", "    ]\n", "\n", "herdnet = LossWrapper(herdnet, losses=losses)"]}, {"cell_type": "markdown", "metadata": {"id": "Nm5u6yg4V78C"}, "source": ["## Create the Trainer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MSBimwtzWDZp"}, "outputs": [], "source": ["from torch.optim import Adam\n", "from animaloc.train import Trainer\n", "from animaloc.eval import PointsMetrics, HerdNetStitcher, HerdNetEvaluator\n", "from animaloc.utils.useful_funcs import mkdir\n", "\n", "work_dir = '/content/output'\n", "mkdir(work_dir)\n", "\n", "lr = 1e-4\n", "weight_decay = 1e-3\n", "epochs = 100\n", "\n", "optimizer = Adam(params=herdnet.parameters(), lr=lr, weight_decay=weight_decay)\n", "\n", "metrics = PointsMetrics(radius=20, num_classes=num_classes)\n", "\n", "stitcher = HerdNetStitcher(\n", "    model=herdnet, \n", "    size=(patch_size,patch_size), \n", "    overlap=160, \n", "    down_ratio=down_ratio, \n", "    reduction='mean'\n", "    )\n", "\n", "evaluator = HerdNetEvaluator(\n", "    model=herdnet, \n", "    dataloader=val_dataloader, \n", "    metrics=metrics, \n", "    stitcher=stitcher, \n", "    work_dir=work_dir, \n", "    header='validation'\n", "    )\n", "\n", "trainer = Trainer(\n", "    model=herdnet,\n", "    train_dataloader=train_dataloader,\n", "    optimizer=optimizer,\n", "    num_epochs=epochs,\n", "    evaluator=evaluator,\n", "    work_dir=work_dir\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "axsTtq4WV0ot"}, "source": ["## Start training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "malFT6r5V4rC"}, "outputs": [], "source": ["trainer.start(warmup_iters=100, checkpoints='best', select='max', validate_on='f1_score')"]}, {"cell_type": "markdown", "metadata": {"id": "_e0CQd5Bxx5T"}, "source": ["## Test the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "D5133GoRz8r_"}, "outputs": [], "source": ["# Path to your .pth file\n", "import gdown\n", "\n", "pth_path = ''\n", "\n", "if not pth_path:\n", "    gdown.download(\n", "        'https://drive.google.com/uc?export=download&id=1-WUnBC4BJMVkNvRqalF_HzA1_pRkQTI_',\n", "        '/content/20220413_herdnet_model.pth'\n", "        )\n", "    pth_path = '/content/20220413_herdnet_model.pth'"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VPHXVYWNzVDj"}, "outputs": [], "source": ["# Create output folder\n", "test_dir = '/content/test_output'\n", "mkdir(test_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DXUsHk7dzl47"}, "outputs": [], "source": ["# Load trained parameters\n", "from animaloc.models import load_model\n", "\n", "herdnet = load_model(herdnet, pth_path=pth_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lX3Jp883zB-D"}, "outputs": [], "source": ["# Create an Evaluator\n", "test_evaluator = HerdNetEvaluator(\n", "    model=herdnet, \n", "    dataloader=test_dataloader, \n", "    metrics=metrics, \n", "    stitcher=stitcher, \n", "    work_dir=test_dir, \n", "    header='test'\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CZt_wNle0488"}, "outputs": [], "source": ["# Start testing\n", "test_f1_score = test_evaluator.evaluate(returns='f1_score')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1vICHF-sFGxa"}, "outputs": [], "source": ["# Print global F1 score (%)\n", "print(f\"F1 score = {test_f1_score * 100:0.0f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ANdn_feR2ZY8"}, "outputs": [], "source": ["# Get the detections\n", "detections = test_evaluator.results\n", "detections"]}], "metadata": {"accelerator": "GPU", "colab": {"authorship_tag": "ABX9TyOEeDjFeLKZlLHdeq13OrD5", "provenance": []}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}